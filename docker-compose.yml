version: '3'
services:
  consul:
    image: consul:1.15.4
    container_name: marvy_consul
    ports:
      - "8500:8500"               # Consul HTTP API
      - "8600:8600/udp"           # DNS Interface
    command: "agent -server -ui -data-dir=/consul/data -bootstrap-expect=1 -client=0.0.0.0"
    # command: "agent -data-dir=/consul/data -bootstrap-expect=1 -server -ui -client=0.0.0.0"
    networks:
      - default
    volumes:
      - ./data/consul:/consul/data
