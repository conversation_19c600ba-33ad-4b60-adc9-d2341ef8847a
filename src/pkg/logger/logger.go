package logger

import (
	"os"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/pkgerrors"
)

type TracingHook struct{}

const RequestIdKey = "rid"

func Init() zerolog.Logger {
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack
	var l = zerolog.New(os.Stdout)
	return l.Hook(TracingHook{})
}

func (h TracingHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	ctx := e.GetCtx()
	r := ctx.Value(RequestIdKey)
	if r != nil {
		requestId := r.(string)
		e.Str(RequestIdKey, requestId)
	}
}

var loggerWithHook = Init()

func Error(err error) *zerolog.Event {
	return loggerWithHook.Error().Stack().Caller(1).Err(err)
}

func Info() *zerolog.Event {
	return loggerWithHook.Info().Caller(1)
}

func Warn() *zerolog.Event {
	return loggerWithHook.Warn().Stack().Caller(1)
}

func Debug() *zerolog.Event {
	return loggerWithHook.Debug().Caller(1)
}

func Fatal(msg string) {
	loggerWithHook.Fatal().Caller(1).Stack().Msg(msg)
}
