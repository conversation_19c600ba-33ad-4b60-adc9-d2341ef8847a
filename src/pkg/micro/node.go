package micro

import (
	"fmt"
	"math/rand"
)

type ServiceInstance struct {
	ID       string
	Address  string
	Port     int
	Healthy  bool
	Metadata map[string]string
}

type Service struct {
	Name      string
	BasePath  string
	Instances []*ServiceInstance
	lastIndex int // For round-robin load balancing
}

func NewService(name string, basePath string) *Service {
	// If basePath is empty, derive it from service name
	if basePath == "" {
		basePath = "/" + name
	}

	return &Service{
		Name:      name,
		BasePath:  basePath,
		Instances: make([]*ServiceInstance, 0),
		lastIndex: -1,
	}
}

func (s *Service) AddInstance(instance *ServiceInstance) {
	s.Instances = append(s.Instances, instance)
}

func (s *Service) RemoveInstance(instanceID string) {
	for i, instance := range s.Instances {
		if instance.ID == instanceID {
			s.Instances = append(s.Instances[:i], s.Instances[i+1:]...)
			break
		}
	}
}

func (s *Service) GetHealthyInstances() []*ServiceInstance {
	healthy := make([]*ServiceInstance, 0)
	for _, instance := range s.Instances {
		if instance.Healthy {
			healthy = append(healthy, instance)
		}
	}
	return healthy
}

// GetInstanceAddress returns an address using round-robin load balancing
func (s *Service) GetInstanceAddress() string {
	healthy := s.GetHealthyInstances()
	if len(healthy) == 0 {
		return ""
	}

	// Round-robin load balancing
	s.lastIndex = (s.lastIndex + 1) % len(healthy)
	instance := healthy[s.lastIndex]

	return fmt.Sprintf("%s:%d", instance.Address, instance.Port)
}

// GetRandomInstanceAddress returns a random healthy instance address
func (s *Service) GetRandomInstanceAddress() string {
	healthy := s.GetHealthyInstances()
	if len(healthy) == 0 {
		return ""
	}

	instance := healthy[rand.Intn(len(healthy))]

	return fmt.Sprintf("%s:%d", instance.Address, instance.Port)
}
