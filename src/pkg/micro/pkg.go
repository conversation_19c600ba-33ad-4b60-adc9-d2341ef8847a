package micro

import (
	"encoding/json"
	"fmt"
	"log"
	"marvy/micro/src/pkg/config"
	"marvy/micro/src/pkg/logger"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"

	"github.com/hashicorp/consul/api"
	"github.com/spf13/viper"
)

type MicroAppConfig struct {
	Name       string
	BasePath   string
	ConsulKeys []string
}

type MicroApp struct {
	name       string
	basePath   string
	consulKeys []string
	router     http.Handler
	serviceID  string
}

func NewMicroApp(config *MicroAppConfig) MicroApp {
	if config.Name == "" {
		panic("micro app name is required")
	}
	if config.BasePath == "" {
		panic("micro app base path is required")
	}
	return MicroApp{
		name:       config.Name,
		basePath:   config.BasePath,
		consulKeys: config.ConsulKeys,
	}
}

func (m *MicroApp) GetName() string {
	return m.name
}

func (m *MicroApp) GetBasePath() string {
	return m.basePath
}

func (m *MicroApp) GetConsulKeys() []string {
	return m.consulKeys
}

func (m *MicroApp) LoadConfig() {
	consulAddr := config.GetConsulAddr()
	err := config.LoadConfigFromConsul(m.consulKeys, consulAddr)
	if err != nil {
		log.Fatal("failed to load config from consul", err)
	}
}

func (m *MicroApp) GetServerPort() string {
	port := viper.GetString("server.port")

	// Fallback to environment variable if not found in config
	if port == "" {
		port = os.Getenv("SERVER_PORT")
	}

	if port == "" {
		panic("server port is required")
	}

	return port
}

func (m *MicroApp) getServiceAddress() string {
	// Try to get pod IP from environment (Kubernetes)
	if podIP := os.Getenv("POD_IP"); podIP != "" {
		return podIP
	}

	// Try to get the outbound IP address
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "localhost" // fallback
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

func (m *MicroApp) StartServer(router http.Handler) {
	m.LoadConfig()

	port := m.GetServerPort()

	m.router = router
	httpServer := &http.Server{
		Addr:    "0.0.0.0:" + port,
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logger.Info().Msgf("Gateway starting on %s:%s", httpServer.Addr, port)
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	err := m.registerWithConsul()
	if err != nil {
		log.Fatal(err)
	}

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info().Msg("Shutting down server...")

	// Deregister service from Consul
	if err := m.DeregisterService(); err != nil {
		logger.Info().Msgf("Failed to deregister service from Consul: %v", err)
	}

	logger.Info().Msg("Server shutdown complete")
}

func (m *MicroApp) registerWithConsul() error {
	// Create Consul client
	consulConfig := api.DefaultConfig()
	consulConfig.Address = config.GetConsulAddr()

	client, err := api.NewClient(consulConfig)
	if err != nil {
		return fmt.Errorf("failed to create consul client: %w", err)
	}

	// Convert port to int
	port, err := strconv.Atoi(m.GetServerPort())
	if err != nil {
		port = 8081 // default
	}

	// Define the routes this API handles with their protection levels
	routes := map[string]any{
		"routes":   []map[string]any{},
		"api_type": "rest",
		"version":  "v1",
	}

	// Convert routes to JSON string for Consul metadata
	routesJSON, err := json.Marshal(routes)
	if err != nil {
		return fmt.Errorf("failed to marshal routes: %w", err)
	}

	// Register Kubernetes Service with Consul instead of individual pods
	// This provides instant failover through Kubernetes Service load balancing
	serviceID := m.name + "-service"
	kubernetesServiceIP := "accounts-api.marvy-micro.svc.cluster.local" // Use DNS name for portability

	registration := &api.AgentServiceRegistration{
		ID:      serviceID,
		Name:    m.name,
		Address: kubernetesServiceIP,
		Port:    port,
		Meta: map[string]string{
			"routes":       string(routesJSON),
			"api_type":     "rest",
			"base_path":    m.basePath,
			"version":      "v1",
			"service_type": "kubernetes-service", // Mark as K8s Service
		},
		Check: &api.AgentServiceCheck{
			HTTP:                           fmt.Sprintf("http://%s:%d/health", kubernetesServiceIP, port),
			Interval:                       "10s", // Less frequent since K8s handles health checks
			Timeout:                        "3s",  // Longer timeout for DNS resolution
			DeregisterCriticalServiceAfter: "30s", // Longer since K8s provides instant failover
		},
	}

	// Check if service is already registered (avoid duplicate registrations)
	services, err := client.Agent().Services()
	if err != nil {
		return fmt.Errorf("failed to list services: %w", err)
	}

	// Only register if not already registered
	if _, exists := services[serviceID]; !exists {
		err = client.Agent().ServiceRegister(registration)
		if err != nil {
			return fmt.Errorf("failed to register service: %w", err)
		}
		logger.Info().Msgf("Successfully registered Kubernetes Service %s with Consul", serviceID)
	} else {
		logger.Info().Msgf("Kubernetes Service %s already registered with Consul", serviceID)
	}

	// Store service ID for later deregistration
	m.serviceID = serviceID

	return nil
}

// DeregisterService removes the service from Consul
// For Kubernetes Services, we don't deregister on pod shutdown since other pods may still be running
func (m *MicroApp) DeregisterService() error {
	if m.serviceID == "" {
		return nil // Nothing to deregister
	}

	// For Kubernetes Service approach, we don't deregister the service when individual pods shut down
	// The service should remain registered as long as the deployment exists
	logger.Info().Msgf("Pod shutting down, but keeping Kubernetes Service %s registered in Consul", m.serviceID)
	return nil
}
