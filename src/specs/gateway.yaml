openapi: 3.0.3
info:
  title: API Gateway
  description: API Gateway for microservices architecture with service discovery and proxying
  version: 1.0.0
  contact:
    name: Microservices Team
    email: <EMAIL>

servers:
  - url: http://localhost:9100
    description: Development server
  - url: https://gateway.microservices.com
    description: Production server

paths:
  /health:
    get:
      summary: Health check endpoint
      operationId: getHealth
      tags:
        - Health
      responses:
        '200':
          description: Gateway is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

components:
  schemas:
    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - healthy
            - unhealthy
        message:
          type: string
          description: Description of the status
