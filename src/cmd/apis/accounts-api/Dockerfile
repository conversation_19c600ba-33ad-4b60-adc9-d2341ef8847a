# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the accounts-api
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o accounts-api ./src/cmd/apis/accounts-api

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/accounts-api .

# Expose port
EXPOSE 8901

# Run the accounts-api
CMD ["./accounts-api"]
