package main

import (
	"fmt"
	"marvy/micro/src/pkg/micro"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func main() {
	app := micro.NewMicroApp(&micro.MicroAppConfig{
		Name:       "apis-account",
		BasePath:   "/accounts",
		ConsulKeys: []string{"apps/accounts.toml"},
	})

	router := echo.New()
	router.Use(middleware.Logger())
	fmt.Println("hallo", app.GetBasePath())

	hanlder := NewAccountHandler()

	hanlder.RegisterRoutes(router)

	app.StartServer(router)
}
