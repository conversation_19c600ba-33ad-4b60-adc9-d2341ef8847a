package proxy

import (
	"fmt"
	"marvy/micro/src/pkg/micro"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestServiceRegistry_FindService(t *testing.T) {
	registry := NewServiceRegistry()

	// Create a service with multiple instances
	service := micro.NewService("user-service", "/users")

	// Add multiple instances
	instance1 := &micro.ServiceInstance{
		ID:      "user-service-1",
		Address: "********",
		Port:    8080,
		Healthy: true,
	}
	instance2 := &micro.ServiceInstance{
		ID:      "user-service-2",
		Address: "********",
		Port:    8080,
		Healthy: true,
	}
	instance3 := &micro.ServiceInstance{
		ID:      "user-service-3",
		Address: "********",
		Port:    8080,
		Healthy: false, // Unhealthy instance
	}

	service.AddInstance(instance1)
	service.AddInstance(instance2)
	service.AddInstance(instance3)

	registry.services["/users"] = service

	tests := []struct {
		name        string
		path        string
		expectError bool
		serviceName string
	}{
		{
			name:        "Find users service",
			path:        "/users/profile",
			expectError: false,
			serviceName: "user-service",
		},
		{
			name:        "Find users service with nested path",
			path:        "/users/123/orders",
			expectError: false,
			serviceName: "user-service",
		},
		{
			name:        "Service not found",
			path:        "/orders/123",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := registry.FindService(tt.path)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, service)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
				assert.Equal(t, tt.serviceName, service.Name)

				// Verify we only get healthy instances
				healthyInstances := service.GetHealthyInstances()
				assert.Len(t, healthyInstances, 2) // Only 2 healthy instances
			}
		})
	}
}

func TestService_LoadBalancing(t *testing.T) {
	service := micro.NewService("test-service", "/test")

	// Add multiple healthy instances
	for i := 1; i <= 3; i++ {
		instance := &micro.ServiceInstance{
			ID:      fmt.Sprintf("test-service-%d", i),
			Address: fmt.Sprintf("10.0.0.%d", i),
			Port:    8080,
			Healthy: true,
		}
		service.AddInstance(instance)
	}

	// Test round-robin load balancing
	addresses := make(map[string]int)
	for i := 0; i < 9; i++ { // 3 rounds of 3 instances
		address := service.GetInstanceAddress()
		addresses[address]++
	}

	// Each instance should be called exactly 3 times
	assert.Len(t, addresses, 3)
	for address, count := range addresses {
		assert.Equal(t, 3, count, "Address %s should be called 3 times", address)
	}
}

func TestService_ConventionBasedBasePath(t *testing.T) {
	tests := []struct {
		name         string
		serviceName  string
		basePath     string
		expectedPath string
	}{
		{
			name:         "Empty basePath uses service name",
			serviceName:  "user-service",
			basePath:     "",
			expectedPath: "/user-service",
		},
		{
			name:         "Custom basePath overrides service name",
			serviceName:  "user-management-service",
			basePath:     "/users",
			expectedPath: "/users",
		},
		{
			name:         "API versioning",
			serviceName:  "user-service-v2",
			basePath:     "/v2/users",
			expectedPath: "/v2/users",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := micro.NewService(tt.serviceName, tt.basePath)
			assert.Equal(t, tt.expectedPath, service.BasePath)
		})
	}
}
