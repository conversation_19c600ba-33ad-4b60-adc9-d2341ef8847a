package proxy

import (
	"fmt"
	"marvy/micro/src/pkg/micro"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
)

type Proxy struct {
	serviceRegistry *ServiceRegistry
}

func NewProxy() *Proxy {
	return &Proxy{
		serviceRegistry: NewServiceRegistry(),
	}
}

func (p *Proxy) InitConsulClient(consulAddr string) error {
	return p.serviceRegistry.InitConsulClient(consulAddr)
}

func (p *Proxy) StartServiceDiscovery() {
	p.serviceRegistry.StartServiceDiscovery()
}

func (p *Proxy) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	fmt.Println("Request:", r.URL.Path)

	// Find service using service registry
	service, err := p.serviceRegistry.FindService(r.URL.Path)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	p.proxyToService(w, r, service)
}

func (p *Proxy) proxyToService(w http.ResponseWriter, r *http.Request, service *micro.Service) {
	// Get a healthy instance using load balancing
	targetAddress := service.GetInstanceAddress()
	if targetAddress == "" {
		http.Error(w, "No healthy instances available", http.StatusServiceUnavailable)
		return
	}

	targetURL, err := url.Parse("http://" + targetAddress)
	if err != nil {
		http.Error(w, "Invalid target URL", http.StatusInternalServerError)
		return
	}

	proxy := httputil.NewSingleHostReverseProxy(targetURL)

	// Configure transport to disable connection pooling for better load balancing
	// This ensures each request creates a new connection, allowing Kubernetes Service
	// to properly distribute requests across different pods
	proxy.Transport = &http.Transport{
		DisableKeepAlives: true, // Force new connection for each request
	}

	// Modify the request to remove the base path prefix
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		// Remove the base path from the URL
		req.URL.Path = strings.TrimPrefix(req.URL.Path, service.BasePath)
		if req.URL.Path == "" {
			req.URL.Path = "/"
		}
		fmt.Printf("Proxying to %s: %s -> %s\n", targetAddress, r.URL.Path, req.URL.Path)
	}

	proxy.ServeHTTP(w, r)
}
