package proxy

import (
	"marvy/micro/src/pkg/micro"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestProxy_ServiceDiscovery(t *testing.T) {
	// Setup
	proxy := NewProxy()

	// Create a mock service registry with a test service
	service := micro.NewService("accounts-service", "/accounts")
	instance := &micro.ServiceInstance{
		ID:      "accounts-1",
		Address: "localhost",
		Port:    8901,
		Healthy: true,
	}
	service.AddInstance(instance)

	// Manually add service to registry for testing
	proxy.serviceRegistry.services["/accounts"] = service

	tests := []struct {
		name        string
		path        string
		expectError bool
		serviceName string
	}{
		{
			name:        "Find accounts service",
			path:        "/accounts/health",
			expectError: false,
			serviceName: "accounts-service",
		},
		{
			name:        "Path not found",
			path:        "/users/health",
			expectError: true,
		},
		{
			name:        "Empty path",
			path:        "/",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := proxy.serviceRegistry.FindService(tt.path)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, service)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
				assert.Equal(t, tt.serviceName, service.Name)
			}
		})
	}
}
