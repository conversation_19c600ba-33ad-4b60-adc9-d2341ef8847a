package proxy

import (
	"fmt"
	"marvy/micro/src/pkg/micro"
	"strings"
	"sync"
	"time"

	"github.com/hashicorp/consul/api"
)

type ServiceRegistry struct {
	services     map[string]*micro.Service // basePath -> Service
	consulClient *api.Client
	mutex        sync.RWMutex
}

func NewServiceRegistry() *ServiceRegistry {
	return &ServiceRegistry{
		services: make(map[string]*micro.Service),
	}
}

func (sr *ServiceRegistry) InitConsulClient(consulAddr string) error {
	config := api.DefaultConfig()
	config.Address = consulAddr

	client, err := api.NewClient(config)
	if err != nil {
		return fmt.Errorf("failed to create consul client: %w", err)
	}

	sr.consulClient = client
	return nil
}

func (sr *ServiceRegistry) StartServiceDiscovery() {
	go func() {
		// Initial discovery
		sr.discoverServices()
		
		// Periodic discovery
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		for {
			<-ticker.C
			sr.discoverServices()
		}
	}()
}

func (sr *ServiceRegistry) discoverServices() {
	if sr.consulClient == nil {
		return
	}

	// Get all services
	serviceMap, _, err := sr.consulClient.Catalog().Services(nil)
	if err != nil {
		fmt.Printf("Error discovering services: %v\n", err)
		return
	}

	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	// Clear existing services
	sr.services = make(map[string]*micro.Service)

	// For each service, get its health information
	for serviceName := range serviceMap {
		if serviceName == "gateway" || serviceName == "consul" {
			continue // Skip gateway and consul itself
		}

		healthServices, _, err := sr.consulClient.Health().Service(serviceName, "", true, nil)
		if err != nil {
			fmt.Printf("Error getting health for service %s: %v\n", serviceName, err)
			continue
		}

		for _, healthService := range healthServices {
			// Extract base path from service metadata
			basePath := sr.extractBasePath(healthService.Service.Meta)
			if basePath == "" {
				// Use service name as base path if not specified
				basePath = "/" + serviceName
			}

			// Get or create service
			service, exists := sr.services[basePath]
			if !exists {
				service = micro.NewService(serviceName, basePath)
				sr.services[basePath] = service
			}

			// Create service instance
			address := healthService.Service.Address
			if address == "" {
				address = healthService.Node.Address
			}

			instance := &micro.ServiceInstance{
				ID:       healthService.Service.ID,
				Address:  address,
				Port:     healthService.Service.Port,
				Healthy:  sr.isInstanceHealthy(healthService.Checks),
				Metadata: healthService.Service.Meta,
			}

			service.AddInstance(instance)
			fmt.Printf("Discovered instance: service=%s, basePath=%s, address=%s:%d, healthy=%v\n", 
				serviceName, basePath, address, healthService.Service.Port, instance.Healthy)
		}
	}

	fmt.Printf("Service discovery complete. Total services: %d\n", len(sr.services))
}

func (sr *ServiceRegistry) extractBasePath(meta map[string]string) string {
	// Try to get base path from service metadata
	if basePath, exists := meta["base_path"]; exists {
		return basePath
	}
	return ""
}

func (sr *ServiceRegistry) isInstanceHealthy(checks api.HealthChecks) bool {
	for _, check := range checks {
		if check.Status != "passing" {
			return false
		}
	}
	return true
}

func (sr *ServiceRegistry) FindService(path string) (*micro.Service, error) {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	pathParts := strings.Split(strings.Trim(path, "/"), "/")
	if len(pathParts) == 0 {
		return nil, fmt.Errorf("no service found for path: %s", path)
	}

	// Try to find the longest matching base path
	for i := len(pathParts); i > 0; i-- {
		servicePath := "/" + strings.Join(pathParts[:i], "/")
		
		if service, exists := sr.services[servicePath]; exists {
			if len(service.GetHealthyInstances()) > 0 {
				return service, nil
			}
		}
	}

	return nil, fmt.Errorf("no healthy service found for path: %s", path)
}

func (sr *ServiceRegistry) GetServices() map[string]*micro.Service {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()
	
	// Return a copy to avoid race conditions
	services := make(map[string]*micro.Service)
	for k, v := range sr.services {
		services[k] = v
	}
	return services
}
