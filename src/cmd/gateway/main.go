package main

import (
	"fmt"
	"marvy/micro/src/cmd/gateway/proxy"
	"marvy/micro/src/pkg/micro"
	"os"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func main() {
	println("hello world!")
	app := micro.NewMicroApp(&micro.MicroAppConfig{
		Name:       "gateway",
		BasePath:   "/api",
		ConsulKeys: []string{"apps/gateway.toml"},
	})

	router := echo.New()
	router.Use(middleware.Logger())

	prxy := proxy.NewProxy()

	// Initialize Consul client for service discovery
	err := prxy.InitConsulClient(os.Getenv("CONSUL_ADDR"))
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize Consul client: %v", err))
	}

	// Start automatic service discovery
	prxy.StartServiceDiscovery()

	// Register proxy to handle all requests that don't match specific routes
	router.Any("/*", echo.WrapHandler(prxy))

	handler := proxy.NewGatewayHandler()

	handler.RegisterRoutes(router)

	app.StartServer(router)
}
