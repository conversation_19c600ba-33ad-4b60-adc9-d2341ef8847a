apiVersion: skaffold/v4beta13
kind: Config
metadata:
  name: marvy-micro

build:
  local:
    push: false
  artifacts:
  - image: gateway
    context: .
    docker:
      dockerfile: src/cmd/gateway/Dockerfile
  - image: accounts-api
    context: .
    docker:
      dockerfile: src/cmd/apis/accounts-api/Dockerfile

manifests:
  rawYaml:
  - k8s/*.yaml

portForward:
- resourceType: service
  resourceName: gateway-external
  namespace: marvy-micro
  port: 8900
  localPort: 8900
