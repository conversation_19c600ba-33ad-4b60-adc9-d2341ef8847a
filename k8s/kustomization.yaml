apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Namespace for all resources
namespace: marvy-micro

# Resources to include
resources:
  - namespace.yaml
  - consul.yaml
  - accounts-api.yaml
  - accounts-api-service.yaml
  - gateway.yaml

# Labels applied to all resources
labels:
  - pairs:
      app.kubernetes.io/name: marvy-micro
      app.kubernetes.io/version: v1.0.0
      app.kubernetes.io/managed-by: skaffold

# Images that will be replaced by Skaffold
images:
  - name: gateway
    newTag: latest
  - name: accounts-api
    newTag: latest

# ConfigMap generator for Consul configuration
configMapGenerator:
  - name: consul-config
    files:
      - gateway.toml
      - accounts.toml
