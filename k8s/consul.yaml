apiVersion: v1
kind: ConfigMap
metadata:
  name: consul-config
  namespace: marvy-micro
data:
  gateway.toml: |
    [server]
    port = "8900"
  accounts.toml: |
    [server]
    port = "8901"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: consul
  namespace: marvy-micro
  labels:
    app: consul
spec:
  replicas: 1
  selector:
    matchLabels:
      app: consul
  template:
    metadata:
      labels:
        app: consul
    spec:
      containers:
      - name: consul
        image: consul:1.15.4
        ports:
        - containerPort: 8500
          name: http
        - containerPort: 8600
          name: dns
        command:
        - consul
        - agent
        - -server
        - -ui
        - -data-dir=/consul/data
        - -bootstrap-expect=1
        - -client=0.0.0.0
        - -bind=0.0.0.0
        volumeMounts:
        - name: consul-data
          mountPath: /consul/data
        - name: consul-config
          mountPath: /consul/config
        env:
        - name: CONSUL_LOCAL_CONFIG
          value: |
            {
              "datacenter": "dc1",
              "data_dir": "/consul/data",
              "log_level": "INFO",
              "server": true,
              "ui_config": {
                "enabled": true
              }
            }
      volumes:
      - name: consul-data
        emptyDir: {}
      - name: consul-config
        configMap:
          name: consul-config
---
apiVersion: v1
kind: Service
metadata:
  name: consul
  namespace: marvy-micro
  labels:
    app: consul
spec:
  selector:
    app: consul
  ports:
  - name: http
    port: 8500
    targetPort: 8500
  - name: dns
    port: 8600
    targetPort: 8600
    protocol: UDP
