apiVersion: apps/v1
kind: Deployment
metadata:
  name: accounts-api
  namespace: marvy-micro
  labels:
    app: accounts-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: accounts-api
  template:
    metadata:
      labels:
        app: accounts-api
    spec:
      initContainers:
      - name: wait-for-consul
        image: curlimages/curl:8.1.2
        command: ['sh', '-c']
        args:
        - |
          echo "Waiting for Consul to be ready..."
          until curl -f http://consul.marvy-micro.svc.cluster.local:8500/v1/status/leader; do
            echo "Consul not ready, waiting..."
            sleep 2
          done
          echo "Consul is ready!"

          echo "Setting up Consul configuration..."
          curl -X PUT http://consul.marvy-micro.svc.cluster.local:8500/v1/kv/apps/accounts.toml \
            -d '[server]
          port = "8901"' || echo "Failed to set accounts config, continuing..."

          curl -X PUT http://consul.marvy-micro.svc.cluster.local:8500/v1/kv/apps/gateway.toml \
            -d '[server]
          port = "8900"' || echo "Failed to set gateway config, continuing..."

          echo "Consul setup complete!"
      containers:
      - name: accounts-api
        image: accounts-api:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8901
        env:
        - name: CONSUL_ADDR
          value: "consul.marvy-micro.svc.cluster.local:8500"
        - name: SERVICE_NAME
          value: "apis-account"
        - name: BASE_PATH
          value: "/accounts"
        - name: SERVER_PORT
          value: "8901"
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: /health
            port: 8901
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8901
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
---
apiVersion: v1
kind: Service
metadata:
  name: accounts-api
  namespace: marvy-micro
  labels:
    app: accounts-api
spec:
  selector:
    app: accounts-api
  ports:
  - name: http
    port: 8901
    targetPort: 8901
  type: ClusterIP
