apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway
  namespace: marvy-micro
  labels:
    app: gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gateway
  template:
    metadata:
      labels:
        app: gateway
    spec:
      initContainers:
      - name: wait-for-consul
        image: curlimages/curl:8.1.2
        command: ['sh', '-c']
        args:
        - |
          echo "Waiting for Consul to be ready..."
          until curl -f http://consul.marvy-micro.svc.cluster.local:8500/v1/status/leader; do
            echo "Consul not ready, waiting..."
            sleep 2
          done
          echo "Consul is ready and configuration should be set up!"
      containers:
      - name: gateway
        image: gateway:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8900
        env:
        - name: CONSUL_ADDR
          value: "consul.marvy-micro.svc.cluster.local:8500"
        - name: SERVICE_NAME
          value: "gateway"
        - name: BASE_PATH
          value: "/api"
        - name: SERVER_PORT
          value: "8900"
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: /health
            port: 8900
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8900
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
---
apiVersion: v1
kind: Service
metadata:
  name: gateway
  namespace: marvy-micro
  labels:
    app: gateway
spec:
  selector:
    app: gateway
  ports:
  - name: http
    port: 8900
    targetPort: 8900
  type: ClusterIP
---
# Port forward service to expose gateway on localhost:8900
apiVersion: v1
kind: Service
metadata:
  name: gateway-external
  namespace: marvy-micro
  labels:
    app: gateway
spec:
  selector:
    app: gateway
  ports:
  - name: http
    port: 8900
    targetPort: 8900
    nodePort: 30900
  type: NodePort
