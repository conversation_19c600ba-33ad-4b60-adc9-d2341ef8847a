# Git
.git
.gitignore

# Documentation
README*.md
*.md

# Development files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Build artifacts
*.exe
*.dll
*.so
*.dylib

# Test files
*_test.go
test/
coverage.out

# Kubernetes and deployment
k8s/
scripts/
skaffold.yaml
docker-compose.yml

# Data and logs
data/
logs/
*.log

# Temporary files
tmp/
temp/

# Node modules (if any)
node_modules/

# Examples and docs
examples/
