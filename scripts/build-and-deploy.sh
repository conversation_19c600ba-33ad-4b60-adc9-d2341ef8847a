#!/bin/bash

# Build images and deploy to Kubernetes

set -e

echo "🔨 Building and deploying Marvy Micro services..."

# Configure Docker to use Minikube's daemon
eval $(minikube docker-env)

# Build the gateway image
echo "🏗️ Building gateway image..."
docker build -f src/cmd/gateway/Dockerfile -t gateway:latest .

# Build the accounts-api image
echo "🏗️ Building accounts-api image..."
docker build -f src/cmd/apis/accounts-api/Dockerfile -t accounts-api:latest .

# Verify images were built
echo "📋 Verifying images..."
docker images | grep -E "(gateway|accounts-api)"

# Create namespace first
echo "📦 Creating namespace..."
kubectl apply -f k8s/namespace.yaml

# Wait for namespace to be ready
sleep 2

# Deploy all services
echo "🚀 Deploying services..."
kubectl apply -f k8s/

# Wait for deployments to be ready
echo "⏳ Waiting for services to be ready..."
kubectl wait --for=condition=ready pod -l app=consul -n marvy-micro --timeout=60s
kubectl wait --for=condition=ready pod -l app=gateway -n marvy-micro --timeout=120s
kubectl wait --for=condition=ready pod -l app=accounts-api -n marvy-micro --timeout=120s

# Show status
echo "✅ Deployment complete!"
kubectl get pods -n marvy-micro

# Set up port forwarding
echo "🔌 Setting up port forwarding..."
kubectl port-forward svc/gateway-external 8900:8900 -n marvy-micro &
PORT_FORWARD_PID=$!

echo "🎉 All services are running!"
echo ""
echo "Gateway is accessible at: http://localhost:8900"
echo "Test with: curl localhost:8900/accounts/health"
echo ""
echo "To stop port forwarding: kill $PORT_FORWARD_PID"
echo "To cleanup: ./scripts/cleanup.sh"
