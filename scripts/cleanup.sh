#!/bin/bash

# Cleanup development environment

set -e

echo "🧹 Cleaning up Marvy Micro development environment..."

# Delete the namespace (this will delete all resources)
kubectl delete namespace marvy-micro --ignore-not-found=true

# Clean up Skaffold artifacts
skaffold delete || true

echo "✅ Cleanup complete!"
echo ""
echo "To stop Minikube completely, run: minikube stop"
echo "To delete Minikube cluster, run: minikube delete"
