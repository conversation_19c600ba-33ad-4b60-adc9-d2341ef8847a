#!/bin/bash

echo "Waiting for Consul to be ready..."
kubectl wait --for=condition=available --timeout=120s deployment/consul -n marvy-micro

echo "Setting up Consul configuration..."
kubectl exec -n marvy-micro deployment/consul -- consul kv put apps/gateway.toml '[server]
port = "8900"' || true

kubectl exec -n marvy-micro deployment/consul -- consul kv put apps/accounts.toml '[server]
port = "8901"' || true

echo "Consul setup complete!"
echo "You can now restart the services or they should automatically recover."
