#!/bin/bash

# Test script to verify the Kubernetes setup is working

set -e

echo "🧪 Testing Marvy Micro Kubernetes setup..."

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
kubectl wait --for=condition=ready pod -l app=consul -n marvy-micro --timeout=60s
kubectl wait --for=condition=ready pod -l app=gateway -n marvy-micro --timeout=60s
kubectl wait --for=condition=ready pod -l app=accounts-api -n marvy-micro --timeout=60s

echo "✅ All pods are ready"

# Test service discovery
echo "🔍 Testing service discovery..."
kubectl get pods -n marvy-micro

# Test the health endpoint
echo "🏥 Testing health endpoint..."
response=$(curl -s localhost:8900/accounts/health || echo "failed")

if [[ "$response" == *"healthy"* ]]; then
    echo "✅ Health endpoint is working: $response"
else
    echo "❌ Health endpoint failed: $response"
    exit 1
fi

# Test load balancing by making multiple requests
echo "⚖️ Testing load balancing..."
for i in {1..5}; do
    response=$(curl -s localhost:8900/accounts/health)
    echo "Request $i: $response"
    sleep 1
done

echo "🎉 All tests passed! Your Kubernetes setup is working correctly."
echo ""
echo "📊 Current status:"
kubectl get all -n marvy-micro
