#!/bin/bash

# Setup development environment with Minikube and Skaffold

set -e

echo "🚀 Setting up Marvy Micro development environment..."

# Check if required tools are installed
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed. Please install it first."
        exit 1
    fi
    echo "✅ $1 is installed"
}

echo "📋 Checking required tools..."
check_tool minikube
check_tool kubectl
check_tool skaffold
check_tool docker

# Start Minikube if not running
echo "🔧 Starting Minikube..."
if ! minikube status | grep -q "Running"; then
    minikube start --driver=docker --memory=4096 --cpus=2
    echo "✅ Minikube started"
else
    echo "✅ Minikube is already running"
fi

# Configure kubectl to use minikube context
kubectl config use-context minikube

# Enable required addons
echo "🔌 Enabling Minikube addons..."
if ! minikube addons list | grep -q "ingress.*enabled"; then
    minikube addons enable ingress
else
    echo "✅ Ingress addon already enabled"
fi

if ! minikube addons list | grep -q "metrics-server.*enabled"; then
    minikube addons enable metrics-server
else
    echo "✅ Metrics-server addon already enabled"
fi

# Set docker environment to use minikube's docker daemon
echo "🐳 Configuring Docker environment..."
eval $(minikube docker-env)

echo "🎯 Development environment is ready!"
echo ""
echo "Next steps:"
echo "1. Run 'skaffold dev' to start development"
echo "2. Access the gateway at http://localhost:8900"
echo "3. Use 'kubectl get pods -n marvy-micro' to check pod status"
echo ""
echo "Useful commands:"
echo "- skaffold dev --port-forward"
echo "- kubectl logs -f deployment/gateway -n marvy-micro"
echo "- kubectl logs -f deployment/accounts-api -n marvy-micro"
echo "- minikube dashboard"
