#!/bin/bash

# Development script for Marvy Micro

set -e

# Configure Docker to use Minikube's daemon
eval $(minikube docker-env)

echo "🚀 Starting Marvy Micro development environment..."

# Create namespace first to avoid timing issues
echo "📦 Creating namespace..."
kubectl apply -f k8s/namespace.yaml

# Wait a moment for namespace to be ready
sleep 2

# Run Skaffold in development mode
echo "🔨 Starting Skaffold development mode..."
skaffold dev --port-forward
