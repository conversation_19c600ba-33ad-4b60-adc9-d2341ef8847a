# Kubernetes Scaling Architecture

## Overview

The improved architecture addresses both redundancy and scalability concerns for Kubernetes deployments.

## Key Improvements

### 1. Service vs BasePath Separation

**Before:**
- ServiceName: `"apis-account"`
- BasePath: `"/accounts"`
- Problem: Redundant and confusing naming

**After:**
- ServiceName: Internal identifier (e.g., `"user-management-service"`)
- BasePath: External API path (e.g., `"/users"`)
- Convention: If BasePath is empty, defaults to `"/${ServiceName}"`

### 2. Multiple Instance Support

**Before:**
```go
// Only one instance per service
map[string]*Node
```

**After:**
```go
// Multiple instances with load balancing
type Service struct {
    Name      string
    BasePath  string
    Instances []*ServiceInstance
}
```

## Kubernetes Example

### Service Registration

When deploying in Kubernetes, each pod registers itself with Consul:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3  # Multiple instances
  template:
    spec:
      containers:
      - name: user-service
        image: user-service:latest
        env:
        - name: SERVICE_NAME
          value: "user-service"
        - name: BASE_PATH
          value: "/users"
        - name: CONSUL_ADDR
          value: "consul.default.svc.cluster.local:8500"
```

### Service Discovery Flow

1. **Pod Startup**: Each pod registers with Consul
   ```
   Service: user-service
   Instances:
   - user-service-pod-1: ***********:8080 (healthy)
   - user-service-pod-2: ***********:8080 (healthy)  
   - user-service-pod-3: ***********:8080 (unhealthy)
   ```

2. **Gateway Discovery**: Gateway discovers all instances
   ```
   BasePath: /users
   Healthy Instances: 2
   Load Balancing: Round-robin
   ```

3. **Request Routing**: 
   ```
   GET /users/123 -> Round-robin to healthy instances
   Request 1: -> ***********:8080/123
   Request 2: -> ***********:8080/123  
   Request 3: -> ***********:8080/123
   ```

### Load Balancing Strategies

```go
// Round-robin (default)
address := service.GetInstanceAddress()

// Random
address := service.GetRandomInstanceAddress()

// Custom strategies can be added
```

### Health Checking

```go
type ServiceInstance struct {
    ID       string
    Address  string
    Port     int
    Healthy  bool  // Based on Consul health checks
    Metadata map[string]string
}
```

### Convention-Based Configuration

```go
// Simple services - BasePath derived from ServiceName
service := micro.NewService("user-service", "")
// Result: BasePath = "/user-service"

// Custom API paths
service := micro.NewService("user-management-service", "/users")
// Result: BasePath = "/users"

// API versioning
service := micro.NewService("user-service-v2", "/v2/users")
// Result: BasePath = "/v2/users"
```

## Benefits for Kubernetes

### 1. **Horizontal Scaling**
- Add/remove pods dynamically
- Automatic load distribution
- Zero-downtime deployments

### 2. **Fault Tolerance**
- Unhealthy instances automatically excluded
- Graceful handling of pod failures
- Health check integration

### 3. **Service Mesh Ready**
- Compatible with Istio/Linkerd
- Observability integration
- Traffic policies support

### 4. **Development Flexibility**
- Convention over configuration
- Override defaults when needed
- Backward compatibility maintained

## Migration Path

1. **Phase 1**: Deploy new architecture alongside existing
2. **Phase 2**: Migrate services to use new Service struct
3. **Phase 3**: Remove legacy Node-based routing
4. **Phase 4**: Add advanced features (circuit breakers, retries, etc.)

This architecture provides a solid foundation for microservices scaling in Kubernetes while maintaining simplicity and flexibility.
