# Marvy Micro - Kubernetes Development Setup

This guide will help you set up a local Kubernetes development environment using Minikube and Skaffold.

## Architecture

- **Consul**: Service discovery and configuration
- **Gateway**: 1 instance, exposed on localhost:8900
- **Accounts API**: 2 instances with load balancing
- **Automatic service discovery** and health checking

## Prerequisites

Install the following tools:

```bash
# Install Minikube
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-darwin-amd64
sudo install minikube-darwin-amd64 /usr/local/bin/minikube

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/darwin/amd64/kubectl"
sudo install kubectl /usr/local/bin/kubectl

# Install Skaffold
curl -Lo skaffold https://storage.googleapis.com/skaffold/releases/latest/skaffold-darwin-amd64
sudo install skaffold /usr/local/bin/
```

Or using Homebrew:
```bash
brew install minikube kubectl skaffold
```

## Quick Start

1. **Setup the development environment:**
   ```bash
   chmod +x scripts/*.sh
   ./scripts/setup-dev.sh
   ```

2. **Start development:**
   ```bash
   ./scripts/dev.sh
   ```

3. **Test the setup:**
   ```bash
   curl localhost:8900/accounts/health
   # Should return: {"status":"healthy"}
   ```

## Manual Setup

If you prefer to run commands manually:

### 1. Start Minikube
```bash
minikube start --driver=docker --memory=4096 --cpus=2
minikube addons enable ingress metrics-server
```

### 2. Configure Docker Environment
```bash
eval $(minikube docker-env)
```

### 3. Deploy with Skaffold
```bash
skaffold dev --port-forward
```

## Architecture Details

### Services
- **Consul**: `consul.marvy-micro.svc.cluster.local:8500`
- **Gateway**: `gateway.marvy-micro.svc.cluster.local:8900`
- **Accounts API**: `accounts-api.marvy-micro.svc.cluster.local:8901`

### Load Balancing
The gateway automatically discovers and load balances between the 2 accounts-api instances using:
- Round-robin load balancing
- Health checking
- Automatic failover

### Service Discovery
Services register themselves with Consul using:
- Pod IP addresses
- Service metadata (base_path, api_type, version)
- Health check endpoints

## Development Workflow

### File Changes
Skaffold watches for file changes and automatically:
1. Rebuilds Docker images
2. Redeploys to Kubernetes
3. Forwards ports

### Debugging

**Check pod status:**
```bash
kubectl get pods -n marvy-micro
```

**View logs:**
```bash
# Gateway logs
kubectl logs -f deployment/gateway -n marvy-micro

# Accounts API logs
kubectl logs -f deployment/accounts-api -n marvy-micro

# Consul logs
kubectl logs -f deployment/consul -n marvy-micro
```

**Port forwarding (if not using Skaffold):**
```bash
kubectl port-forward svc/gateway-external 8900:8900 -n marvy-micro
```

**Access Consul UI:**
```bash
kubectl port-forward svc/consul 8500:8500 -n marvy-micro
# Then open http://localhost:8500
```

### Testing Load Balancing

Test that requests are distributed across multiple accounts-api instances:

```bash
# Make multiple requests and check logs
for i in {1..10}; do
  curl localhost:8900/accounts/health
  sleep 1
done

# Check which pods handled the requests
kubectl logs deployment/accounts-api -n marvy-micro
```

## Cleanup

```bash
./scripts/cleanup.sh
```

Or manually:
```bash
kubectl delete namespace marvy-micro
skaffold delete
minikube stop  # Optional: stop Minikube
```

## Troubleshooting

### Common Issues

1. **Port 8900 not accessible:**
   - Check if port forwarding is working: `kubectl get svc -n marvy-micro`
   - Verify Skaffold port forwarding: `skaffold dev --port-forward`

2. **Services not discovering each other:**
   - Check Consul: `kubectl port-forward svc/consul 8500:8500 -n marvy-micro`
   - Verify service registration in Consul UI

3. **Pods not starting:**
   - Check pod status: `kubectl describe pod <pod-name> -n marvy-micro`
   - Check resource limits and node capacity

4. **Image pull errors:**
   - Ensure Docker environment is set: `eval $(minikube docker-env)`
   - Rebuild images: `skaffold build`

### Useful Commands

```bash
# Restart development
skaffold delete && skaffold dev --port-forward

# Scale accounts-api
kubectl scale deployment accounts-api --replicas=3 -n marvy-micro

# Check service endpoints
kubectl get endpoints -n marvy-micro

# View all resources
kubectl get all -n marvy-micro
```

## Next Steps

- Add more services (users, orders, etc.)
- Implement circuit breakers and retries
- Add monitoring with Prometheus/Grafana
- Set up CI/CD pipelines
- Add API versioning
